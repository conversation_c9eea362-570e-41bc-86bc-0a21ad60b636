'use client';

import { io, Socket } from 'socket.io-client';
import { notificationStore } from '@/store/notificationStore';
import { SOCKET_EVENTS, SOCKET_CONFIG } from '@/lib/socket-config';
import { accessTokenStore } from '@/store/accessToken';

// Types for socket service
type ConnectionStatus = 'connected' | 'connecting' | 'disconnected' | 'offline';
type PresenceStatus = 'online' | 'offline' | 'away' | 'busy';

interface SocketState {
  socket: Socket | null;
  isConnected: boolean;
  connectionAttempted: boolean;
  reconnectAttempts: number;
}

// Modern functional approach with state management
const createSocketService = (url: string) => {
  const maxReconnectAttempts = 3;

  let state: SocketState = {
    socket: null,
    isConnected: false,
    connectionAttempted: false,
    reconnectAttempts: 0,
  };

  const connect = () => {
    if (state.socket?.connected) {
      console.log('Socket.IO is already connected');
      return;
    }

    if (state.connectionAttempted && state.reconnectAttempts >= maxReconnectAttempts) {
      console.log('Socket.IO: Max reconnection attempts reached. Running in offline mode.');
      return;
    }

    state.connectionAttempted = true;

    // Get the current token from the store
    const token = accessTokenStore.accessToken;

    const config = {
      ...SOCKET_CONFIG,
      auth: {
        token: token || 'no-token'
      }
    };

    state.socket = io(url, config);

    state.socket.on('connect', () => {
      console.log('Socket.IO connected successfully');
      state.isConnected = true;
      state.reconnectAttempts = 0; // Reset on successful connection
    });

    state.socket.on('disconnect', (reason) => {
      console.log('Socket.IO disconnected:', reason);
      state.isConnected = false;
    });

    state.socket.on('connect_error', (error) => {
      console.warn('Socket.IO connection failed:', error.message);
      state.reconnectAttempts++;

      if (state.reconnectAttempts >= maxReconnectAttempts) {
        console.log('Socket.IO: Running in offline mode - real-time features disabled');
        state.socket?.disconnect();
      }
    });

    // Set up notification listeners
    setupNotificationListeners();
  };

  const setupNotificationListeners = () => {
    if (!state.socket) return;

    // General notification handler
    state.socket.on(SOCKET_EVENTS.NOTIFICATION, (data: any) => {
      notificationStore.addNotification({
        title: data.title,
        message: data.message,
        link: data.link,
      });
    });

    // Forum message notifications
    state.socket.on(SOCKET_EVENTS.DIRECT_MESSAGE_RECEIVED, (data: any) => {
      notificationStore.addNotification({
        title: 'New Message',
        message: `New message from ${data.sender}: ${data.preview}`,
        link: `/forum/direct-messages/${data.senderId}`,
      });
    });

    // Channel message notifications
    state.socket.on(SOCKET_EVENTS.MESSAGE_DELIVERED, (data: any) => {
      notificationStore.addNotification({
        title: 'New Channel Message',
        message: `New message in ${data.groupName}: ${data.preview}`,
        link: `/forum/${data.groupId}`,
      });
    });

    // Game update notifications
    state.socket.on(SOCKET_EVENTS.GAME_UPDATE, (data: any) => {
      if (data.type === 'game_won' || data.type === 'prize_awarded') {
        notificationStore.addNotification({
          title: data.title || 'Game Update',
          message: data.message,
          link: data.link || '/forum/games',
        });
      }
    });
  };



  const disconnect = () => {
    if (state.socket) {
      state.socket.disconnect();
      state.socket = null;
    }
    state.isConnected = false;
    state.connectionAttempted = false;
    state.reconnectAttempts = 0;
  };

  // Reconnect with updated authentication token
  const reconnectWithAuth = () => {
    console.log('Reconnecting socket with updated authentication...');
    disconnect();
    connect();
  };

  const emit = (event: string, data: any) => {
    if (state.socket?.connected) {
      state.socket.emit(event, data);
    } else {
      // Only log for important events, not routine ones
      if (!['status_update', 'user_typing', 'user_stopped_typing'].includes(event)) {
        console.debug('Socket.IO not connected - event queued:', event);
      }
    }
  };

  const on = (event: string, callback: (...args: any[]) => void) => {
    state.socket?.on(event, callback);
  };

  const off = (event: string, callback: (...args: any[]) => void) => {
    state.socket?.off(event, callback);
  };



  const joinGroup = (groupId: string) => {
    emit(SOCKET_EVENTS.JOIN_GROUP, { groupId });
  };

  const leaveGroup = (groupId: string) => {
    emit(SOCKET_EVENTS.LEAVE_GROUP, { groupId });
  };

  const sendTypingIndicator = (
    groupId?: string,
    recipientId?: string,
    isTyping: boolean = true
  ) => {
    if (groupId) {
      emit(isTyping ? SOCKET_EVENTS.USER_TYPING : SOCKET_EVENTS.USER_STOPPED_TYPING, { groupId });
    } else if (recipientId) {
      emit(isTyping ? SOCKET_EVENTS.DM_USER_TYPING : SOCKET_EVENTS.DM_USER_STOPPED_TYPING, { recipientId });
    }
  };

  const updatePresence = (status: PresenceStatus) => {
    emit(SOCKET_EVENTS.UPDATE_PRESENCE, { status });
  };

  const markMessageAsRead = (messageId: string, isDM: boolean = false) => {
    if (isDM) {
      emit(SOCKET_EVENTS.DM_MESSAGE_READ, { messageId });
    } else {
      emit(SOCKET_EVENTS.MESSAGES_READ, { messageId });
    }
  };

  const joinGame = (gameId: string) => {
    emit(SOCKET_EVENTS.JOIN_GAME, { gameId });
  };

  const leaveGame = (gameId: string) => {
    emit(SOCKET_EVENTS.LEAVE_GAME, { gameId });
  };

  const sendGameAction = (gameId: string, action: string, data: any) => {
    emit(SOCKET_EVENTS.GAME_ACTION, { gameId, action, ...data });
  };

  const isConnectedToServer = (): boolean => {
    return state.isConnected && state.socket?.connected === true;
  };

  const isOfflineMode = (): boolean => {
    return state.connectionAttempted && state.reconnectAttempts >= maxReconnectAttempts;
  };

  const getConnectionStatus = (): ConnectionStatus => {
    if (state.isConnected) return 'connected';
    if (isOfflineMode()) return 'offline';
    if (state.connectionAttempted) return 'connecting';
    return 'disconnected';
  };

  // Return the public API
  return {
    connect,
    disconnect,
    reconnectWithAuth,
    emit,
    on,
    off,
    joinGroup,
    leaveGroup,
    sendTypingIndicator,
    updatePresence,
    markMessageAsRead,
    joinGame,
    leaveGame,
    sendGameAction,
    isConnectedToServer,
    isOfflineMode,
    getConnectionStatus,
  };
};

// Create a singleton instance
// Replace with your actual Socket.IO server URL
const socketService = createSocketService(
  process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000'
);

export default socketService;
