'use client';

import { io, Socket } from 'socket.io-client';
import { notificationStore } from '@/store/notificationStore';
import { SOCKET_EVENTS, SOCKET_CONFIG } from '@/lib/socket-config';
import { accessTokenStore } from '@/store/accessToken';

interface UserPresence {
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: string;
  emoji?: string;
  customMessage?: string;
  clearAfter?: Date;
}

interface TypingIndicator {
  userId: string;
  userName: string;
  groupId?: string;
  recipientId?: string;
  isTyping: boolean;
}

interface MessageDelivery {
  messageId: string;
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
}

class SocketIOService {
  private socket: Socket | null = null;
  private url: string;
  private options: any;
  private isConnected = false;
  private connectionAttempted = false;
  private maxReconnectAttempts = 3;
  private reconnectAttempts = 0;

  constructor(url: string, options?: any) {
    this.url = url;
    this.options = options;
  }

  connect() {
    if (this.socket?.connected) {
      console.log('Socket.IO is already connected');
      return;
    }

    if (this.connectionAttempted && this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Socket.IO: Max reconnection attempts reached. Running in offline mode.');
      return;
    }

    this.connectionAttempted = true;

    // Get the current token from the store
    const token = accessTokenStore.accessToken;

    const config = {
      ...SOCKET_CONFIG,
      ...this.options,
      auth: {
        ...this.options?.auth,
        token: token || 'no-token'
      }
    };

    this.socket = io(this.url, config);

    this.socket.on('connect', () => {
      console.log('Socket.IO connected successfully');
      this.isConnected = true;
      this.reconnectAttempts = 0; // Reset on successful connection
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket.IO disconnected:', reason);
      this.isConnected = false;
    });

    this.socket.on('connect_error', (error) => {
      console.warn('Socket.IO connection failed:', error.message);
      this.reconnectAttempts++;

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log('Socket.IO: Running in offline mode - real-time features disabled');
        this.socket?.disconnect();
      }
    });

    // Set up notification listeners
    this.setupNotificationListeners();
  }

  private setupNotificationListeners() {
    if (!this.socket) return;

    // General notification handler
    this.socket.on(SOCKET_EVENTS.NOTIFICATION, (data: any) => {
      notificationStore.addNotification({
        title: data.title,
        message: data.message,
        link: data.link,
      });
    });

    // Forum message notifications
    this.socket.on(SOCKET_EVENTS.DIRECT_MESSAGE_RECEIVED, (data: any) => {
      notificationStore.addNotification({
        title: 'New Message',
        message: `New message from ${data.sender}: ${data.preview}`,
        link: `/forum/direct-messages/${data.senderId}`,
      });
    });

    // Channel message notifications
    this.socket.on(SOCKET_EVENTS.MESSAGE_DELIVERED, (data: any) => {
      notificationStore.addNotification({
        title: 'New Channel Message',
        message: `New message in ${data.groupName}: ${data.preview}`,
        link: `/forum/${data.groupId}`,
      });
    });

    // Game update notifications
    this.socket.on(SOCKET_EVENTS.GAME_UPDATE, (data: any) => {
      if (data.type === 'game_won' || data.type === 'prize_awarded') {
        notificationStore.addNotification({
          title: data.title || 'Game Update',
          message: data.message,
          link: data.link || '/forum/games',
        });
      }
    });
  }



  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
  }

  emit(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      // Only log for important events, not routine ones
      if (!['status_update', 'user_typing', 'user_stopped_typing'].includes(event)) {
        console.debug('Socket.IO not connected - event queued:', event);
      }
    }
  }

  on(event: string, callback: (...args: any[]) => void) {
    this.socket?.on(event, callback);
  }

  off(event: string, callback: (...args: any[]) => void) {
    this.socket?.off(event, callback);
  }



  joinGroup(groupId: string) {
    this.emit(SOCKET_EVENTS.JOIN_GROUP, { groupId });
  }

  leaveGroup(groupId: string) {
    this.emit(SOCKET_EVENTS.LEAVE_GROUP, { groupId });
  }

  sendTypingIndicator(
    groupId?: string,
    recipientId?: string,
    isTyping: boolean = true
  ) {
    if (groupId) {
      this.emit(isTyping ? SOCKET_EVENTS.USER_TYPING : SOCKET_EVENTS.USER_STOPPED_TYPING, { groupId });
    } else if (recipientId) {
      this.emit(isTyping ? SOCKET_EVENTS.DM_USER_TYPING : SOCKET_EVENTS.DM_USER_STOPPED_TYPING, { recipientId });
    }
  }

  updatePresence(status: 'online' | 'offline' | 'away' | 'busy') {
    this.emit(SOCKET_EVENTS.UPDATE_PRESENCE, { status });
  }

  markMessageAsRead(messageId: string, isDM: boolean = false) {
    if (isDM) {
      this.emit(SOCKET_EVENTS.DM_MESSAGE_READ, { messageId });
    } else {
      this.emit(SOCKET_EVENTS.MESSAGES_READ, { messageId });
    }
  }

  joinGame(gameId: string) {
    this.emit(SOCKET_EVENTS.JOIN_GAME, { gameId });
  }

  leaveGame(gameId: string) {
    this.emit(SOCKET_EVENTS.LEAVE_GAME, { gameId });
  }

  sendGameAction(gameId: string, action: string, data: any) {
    this.emit(SOCKET_EVENTS.GAME_ACTION, { gameId, action, ...data });
  }

  isConnectedToServer(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  isOfflineMode(): boolean {
    return this.connectionAttempted && this.reconnectAttempts >= this.maxReconnectAttempts;
  }

  getConnectionStatus(): 'connected' | 'connecting' | 'disconnected' | 'offline' {
    if (this.isConnected) return 'connected';
    if (this.isOfflineMode()) return 'offline';
    if (this.connectionAttempted) return 'connecting';
    return 'disconnected';
  }
}

// Create a singleton instance
// Replace with your actual Socket.IO server URL
const socketService = new SocketIOService(
  process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:5000'
);

export default socketService;
